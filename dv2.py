import time
import hashlib
import requests
import re
import urllib.parse
import sqlite3
import os
import json
import threading
import queue
from datetime import datetime, timezone, timedelta
from concurrent.futures import ThreadPoolExecutor, as_completed
from typing import List, Dict, Optional, Tuple
import logging

# --- 1. 定义常量和基础配置 ---
BASE_HOST = "31.hxiopkse.icu:9013"  # 默认主机，作为备用
APP_ID = "hymn"
APP_KEY = "shigeben"
USER_AGENT = "okhttp/4.9.0"
OS = "android"
PRC = timezone(timedelta(hours=8))

# 多服务器配置
DOWNLOAD_HOSTS = [
    {"group": 3, "type": 3, "url": "https://***************:9021/api"},
    {"group": 1, "type": 3, "url": "https://11.hxiopkse.icu:9013/api"},
    {"group": 2, "type": 1, "url": "https://21.hxiopkse.icu:9013/api"},
    {"group": 3, "type": 3, "url": "https://31.hxiopkse.icu:9013/api"},
    {"group": 3, "type": 3, "url": "https://32.hxiopkse.icu:9013/api"},
    {"group": 3, "type": 1, "url": "https://33.hxiopkse.icu:9013/api"},
    {"group": 3, "type": 1, "url": "https://34.hxiopkse.icu:9013/api"},
    {"group": 3, "type": 3, "url": "https://35.hxiopkse.icu:9013/api"},
]

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('download.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# --- 2. 算法实现函数 ---

def get_header_timestamp_and_token():
    """生成用于请求头的 timestamp 和 token"""
    ts_header = int(time.time())
    source_string = f"{APP_ID}{APP_KEY}{ts_header}"
    token = hashlib.md5(source_string.encode('utf-8')).hexdigest()
    return ts_header, token.upper()

def get_url_timestamp_and_code(item_id: int):
    """生成用于 URL 的 timestamp 和 code"""
    base_date = datetime(2010, 1, 1, tzinfo=PRC)
    base_ts_millis = int(base_date.timestamp())
    current_ts_millis = int(time.time())
    ts_url = (current_ts_millis - base_ts_millis)
    
    source_string_for_code = f"{ts_url}{item_id}"
    md5_hash = hashlib.md5(source_string_for_code.encode('utf-8')).hexdigest()
    code = md5_hash[12:16]
    
    return ts_url, code.upper()

# --- 3. 数据库和状态管理 ---

class DownloadManager:
    def __init__(self, db_path: str, state_file: str = 'download_state.json'):
        self.db_path = db_path
        self.state_file = state_file
        self.error_file = 'download_errors.json'
        self.lock = threading.Lock()
        self.stats = {
            'total_items': 0,
            'completed': 0,
            'failed': 0,
            'skipped': 0,
            'total_size': 0,
            'downloaded_size': 0,
            'start_time': None,
            'errors': []
        }
        self.load_state()
    
    def load_state(self):
        """加载下载状态"""
        if os.path.exists(self.state_file):
            try:
                with open(self.state_file, 'r', encoding='utf-8') as f:
                    self.stats.update(json.load(f))
                logger.info(f"加载状态: 已完成 {self.stats['completed']}, 失败 {self.stats['failed']}")
            except Exception as e:
                logger.error(f"加载状态文件失败: {e}")
    
    def save_state(self):
        """保存下载状态"""
        try:
            with open(self.state_file, 'w', encoding='utf-8') as f:
                json.dump(self.stats, f, ensure_ascii=False, indent=2)
        except Exception as e:
            logger.error(f"保存状态文件失败: {e}")
    
    def save_error(self, error_info: dict):
        """保存错误信息"""
        try:
            errors = []
            if os.path.exists(self.error_file):
                with open(self.error_file, 'r', encoding='utf-8') as f:
                    errors = json.load(f)
            
            errors.append({
                **error_info,
                'timestamp': datetime.now().isoformat()
            })
            
            with open(self.error_file, 'w', encoding='utf-8') as f:
                json.dump(errors, f, ensure_ascii=False, indent=2)
        except Exception as e:
            logger.error(f"保存错误信息失败: {e}")
    
    def get_download_list(self, table_name: str = 'Image', condition: str = None) -> List[Dict]:
        """获取下载列表"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 构建查询语句，获取id, gid, fs字段
            base_query = f"SELECT id, gid, COALESCE(fs, 0) as fs FROM {table_name}"
            if condition:
                query = f"{base_query} WHERE {condition}"
            else:
                query = base_query
            
            cursor.execute(query)
            rows = cursor.fetchall()
            
            # 构建下载列表
            download_list = []
            for row in rows:
                item_id, gid, fs = row
                # 添加图片下载任务
                download_list.append({
                    'id': item_id,
                    'type': 'image',
                    'size': fs or 0,
                    'gid': gid
                })
                # 如果有gid，添加音乐下载任务
                if gid:
                    download_list.append({
                        'id': gid,
                        'type': 'music',
                        'size': fs or 0,  # 音乐文件大小可能需要另外估算
                        'gid': gid
                    })
            
            conn.close()
            return download_list
            
        except Exception as e:
            logger.error(f"获取下载列表失败: {e}")
            return []
    
    def get_total_size(self, table_name: str = 'Image', condition: str = None) -> int:
        """获取总文件大小"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            base_query = f"SELECT SUM(COALESCE(fs, 0)) FROM {table_name}"
            if condition:
                query = f"{base_query} WHERE {condition}"
            else:
                query = base_query
            
            cursor.execute(query)
            result = cursor.fetchone()
            conn.close()
            
            return result[0] if result and result[0] else 0
            
        except Exception as e:
            logger.error(f"获取总大小失败: {e}")
            return 0
    
    def is_completed(self, item_id: int, download_type: str, output_dir: str) -> bool:
        """检查文件是否已下载完成"""
        file_extension = "jpg" if download_type == "image" else "mp3"
        file_patterns = [
            f"{download_type}_{item_id}.{file_extension}",
            f"{item_id}.{file_extension}"
        ]
        
        for pattern in file_patterns:
            file_path = os.path.join(output_dir, pattern)
            if os.path.exists(file_path) and os.path.getsize(file_path) > 0:
                return True
        return False
    
    def update_progress(self, success: bool, file_size: int = 0, error_info: dict = None):
        """更新下载进度"""
        with self.lock:
            if success:
                self.stats['completed'] += 1
                self.stats['downloaded_size'] += file_size
            else:
                self.stats['failed'] += 1
                if error_info:
                    self.save_error(error_info)
            
            self.save_state()
    
    def get_progress_info(self) -> Dict:
        """获取进度信息"""
        with self.lock:
            total = self.stats['total_items']
            completed = self.stats['completed']
            failed = self.stats['failed']
            remaining = total - completed - failed
            
            # 计算预计剩余时间
            if self.stats['start_time'] and completed > 0:
                elapsed = time.time() - self.stats['start_time']
                rate = completed / elapsed  # 每秒完成数
                eta = remaining / rate if rate > 0 else 0
            else:
                eta = 0
            
            # 计算下载速度
            if self.stats['start_time']:
                elapsed = time.time() - self.stats['start_time']
                speed = self.stats['downloaded_size'] / elapsed if elapsed > 0 else 0
            else:
                speed = 0
            
            return {
                'total': total,
                'completed': completed,
                'failed': failed,
                'remaining': remaining,
                'percentage': (completed / total * 100) if total > 0 else 0,
                'eta_seconds': eta,
                'speed_bps': speed,
                'downloaded_size': self.stats['downloaded_size'],
                'total_size': self.stats['total_size']
            }

# --- 4. 下载功能 ---

def download_single_file(item_id: int, download_type: str, output_dir: str, 
                        max_retries: int = 3, timeout: int = 30) -> Tuple[bool, int, Optional[str]]:
    """下载单个文件"""
    session = None
    
    for attempt in range(max_retries):
        try:
            # 每次重试都创建新的session，避免连接复用问题
            if session:
                session.close()
            
            session = requests.Session()
            
            # 配置SSL和连接参数
            session.verify = False  # 忽略SSL证书验证
            session.trust_env = False  # 忽略环境代理设置
            
            # 配置适配器，增强连接稳定性
            from requests.adapters import HTTPAdapter
            from urllib3.util.retry import Retry
            
            # 只对连接级别的错误重试，不对HTTP状态码重试
            retry_strategy = Retry(
                total=0,  # 不使用urllib3的重试，我们自己控制
                connect=0,
                read=0,
                redirect=0,
                status_forcelist=[]
            )
            
            adapter = HTTPAdapter(
                max_retries=retry_strategy,
                pool_connections=1,
                pool_maxsize=1,
                pool_block=True
            )
            
            session.mount("http://", adapter)
            session.mount("https://", adapter)
            
            # 生成请求参数
            ts_header, token = get_header_timestamp_and_token()
            ts_url, code = get_url_timestamp_and_code(item_id)
            
            BASE_URL = f"https://{BASE_HOST}/api/download/{download_type}"
            final_url = f"{BASE_URL}?id={item_id}&timestamp={ts_url}&code={code}"
            
            headers = {
                "host": BASE_HOST,
                "appid": APP_ID,
                "timestamp": str(ts_header),
                "token": token,
                "os": OS,
                "accept-encoding": "gzip",
                "user-agent": USER_AGENT,
                "Connection": "close",  # 强制关闭连接，避免连接复用问题
            }
            
            # 发起请求，增加更多容错处理
            response = session.get(
                final_url, 
                headers=headers, 
                stream=True,
                timeout=(10, timeout),  # (连接超时, 读取超时)
                allow_redirects=True
            )
            
            if response.status_code == 200:
                # 获取文件名
                file_extension = "jpg" if download_type == "image" else "mp3"
                file_name = f"{download_type}_{item_id}.{file_extension}"
                
                content_disposition = response.headers.get('content-disposition')
                if content_disposition:
                    filename_star_match = re.search(r"filename\*\=UTF-8''(.+)", content_disposition)
                    if filename_star_match:
                        file_name = urllib.parse.unquote(filename_star_match.group(1))
                    else:
                        fname = re.findall(r'filename="?([^"]+)"?', content_disposition)
                        if fname:
                            file_name = fname[0]
                
                # 确保输出目录存在
                os.makedirs(output_dir, exist_ok=True)
                file_path = os.path.join(output_dir, file_name)
                
                # 下载文件，增加错误处理
                file_size = 0
                temp_file_path = file_path + ".tmp"
                
                try:
                    with open(temp_file_path, "wb") as f:
                        for chunk in response.iter_content(chunk_size=8192):
                            if chunk:
                                f.write(chunk)
                                file_size += len(chunk)
                    
                    # 下载完成后重命名
                    os.rename(temp_file_path, file_path)
                    
                    logger.info(f"下载成功: {file_name} ({file_size} bytes)")
                    return True, file_size, None
                    
                except Exception as write_error:
                    # 清理临时文件
                    if os.path.exists(temp_file_path):
                        os.remove(temp_file_path)
                    raise write_error
                    
            else:
                error_msg = f"HTTP {response.status_code}: {response.text[:200]}"
                if attempt == max_retries - 1:
                    logger.error(f"下载失败 (ID: {item_id}, Type: {download_type}): {error_msg}")
                    return False, 0, error_msg
                else:
                    logger.warning(f"下载失败，重试 {attempt + 1}/{max_retries} (ID: {item_id}): {error_msg}")
                    # 增加随机延迟，避免同时重试
                    import random
                    delay = (2 ** attempt) + random.uniform(0, 1)
                    time.sleep(delay)
                    
        except (requests.exceptions.SSLError, 
                requests.exceptions.ConnectionError,
                requests.exceptions.Timeout,
                requests.exceptions.ChunkedEncodingError) as network_error:
            error_msg = f"网络错误: {str(network_error)}"
            if attempt == max_retries - 1:
                logger.error(f"网络异常 (ID: {item_id}, Type: {download_type}): {error_msg}")
                return False, 0, error_msg
            else:
                logger.warning(f"网络异常，重试 {attempt + 1}/{max_retries} (ID: {item_id}): {error_msg}")
                # 网络错误时增加更长的延迟
                import random
                delay = (3 ** attempt) + random.uniform(1, 3)
                time.sleep(delay)
                
        except Exception as e:
            error_msg = f"未知错误: {str(e)}"
            if attempt == max_retries - 1:
                logger.error(f"下载异常 (ID: {item_id}, Type: {download_type}): {error_msg}")
                return False, 0, error_msg
            else:
                logger.warning(f"下载异常，重试 {attempt + 1}/{max_retries} (ID: {item_id}): {error_msg}")
                time.sleep(2 ** attempt)
        
        finally:
            # 确保关闭session
            if session:
                session.close()
    
    return False, 0, "重试次数已用完"

def download_worker(download_queue: queue.Queue, manager: DownloadManager, 
                   output_dir: str, max_retries: int):
    """下载工作线程"""
    while True:
        try:
            item = download_queue.get_nowait()
        except queue.Empty:
            break
        
        item_id = item['id']
        download_type = item['type']
        expected_size = item['size']
        
        # 检查是否已完成
        if manager.is_completed(item_id, download_type, output_dir):
            manager.update_progress(True, expected_size)
            manager.stats['skipped'] += 1
            logger.info(f"跳过已存在文件: {download_type}_{item_id}")
            download_queue.task_done()
            continue
        
        # 下载文件
        success, file_size, error_msg = download_single_file(
            item_id, download_type, output_dir, max_retries
        )
        
        # 更新进度
        error_info = None
        if not success:
            error_info = {
                'id': item_id,
                'type': download_type,
                'error': error_msg,
                'expected_size': expected_size
            }
        
        manager.update_progress(success, file_size, error_info)
        download_queue.task_done()

def progress_monitor(manager: DownloadManager, update_interval: int = 5):
    """进度监控线程"""
    while True:
        try:
            progress = manager.get_progress_info()
            
            if progress['remaining'] <= 0:
                break
            
            # 格式化输出
            percentage = progress['percentage']
            completed = progress['completed']
            total = progress['total']
            failed = progress['failed']
            remaining = progress['remaining']
            
            # 格式化时间
            eta_seconds = progress['eta_seconds']
            if eta_seconds > 0:
                eta_hours = int(eta_seconds // 3600)
                eta_minutes = int((eta_seconds % 3600) // 60)
                eta_str = f"{eta_hours:02d}:{eta_minutes:02d}:{int(eta_seconds % 60):02d}"
            else:
                eta_str = "计算中..."
            
            # 格式化速度
            speed = progress['speed_bps']
            if speed > 1024 * 1024:
                speed_str = f"{speed / (1024 * 1024):.1f} MB/s"
            elif speed > 1024:
                speed_str = f"{speed / 1024:.1f} KB/s"
            else:
                speed_str = f"{speed:.1f} B/s"
            
            # 格式化大小
            downloaded_mb = progress['downloaded_size'] / (1024 * 1024)
            total_mb = progress['total_size'] / (1024 * 1024)
            
            print(f"\r进度: {percentage:.1f}% ({completed}/{total}) "
                  f"失败: {failed} 剩余: {remaining} "
                  f"ETA: {eta_str} 速度: {speed_str} "
                  f"大小: {downloaded_mb:.1f}/{total_mb:.1f} MB", end='', flush=True)
            
            time.sleep(update_interval)
            
        except Exception as e:
            logger.error(f"进度监控错误: {e}")
            break

# --- 5. 主要下载函数 ---

def batch_download_advanced(db_path: str, table_name: str = 'Image',
                           condition: str = None, output_dir: str = 'downloads',
                           max_workers: int = 5, max_retries: int = 3,
                           download_images: bool = True, download_music: bool = True):
    """高级批量下载"""
    
    # 初始化管理器
    manager = DownloadManager(db_path)
    
    # 获取下载列表
    logger.info("获取下载列表...")
    all_items = manager.get_download_list(table_name, condition)
    
    # 过滤下载类型
    download_items = []
    for item in all_items:
        if (item['type'] == 'image' and download_images) or \
           (item['type'] == 'music' and download_music):
            download_items.append(item)
    
    if not download_items:
        logger.info("没有找到需要下载的项目")
        return
    
    # 初始化统计信息
    manager.stats['total_items'] = len(download_items)
    manager.stats['total_size'] = manager.get_total_size(table_name, condition)
    manager.stats['start_time'] = time.time()
    
    logger.info(f"开始下载 {len(download_items)} 个项目")
    logger.info(f"预计总大小: {manager.stats['total_size'] / (1024 * 1024):.1f} MB")
    
    # 创建下载队列
    download_queue = queue.Queue()
    for item in download_items:
        download_queue.put(item)
    
    # 启动进度监控
    progress_thread = threading.Thread(target=progress_monitor, args=(manager, 5))
    progress_thread.daemon = True
    progress_thread.start()
    
    # 启动下载线程
    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        futures = []
        for _ in range(max_workers):
            future = executor.submit(download_worker, download_queue, manager, 
                                   output_dir, max_retries)
            futures.append(future)
        
        # 等待所有任务完成
        for future in as_completed(futures):
            try:
                future.result()
            except Exception as e:
                logger.error(f"下载线程异常: {e}")
    
    # 等待进度监控结束
    progress_thread.join(timeout=1)
    
    # 最终统计
    final_progress = manager.get_progress_info()
    print(f"\n\n=== 下载完成 ===")
    print(f"总计: {final_progress['total']}")
    print(f"成功: {final_progress['completed']}")
    print(f"失败: {final_progress['failed']}")
    print(f"跳过: {manager.stats['skipped']}")
    print(f"成功率: {final_progress['completed'] / final_progress['total'] * 100:.1f}%")
    print(f"下载大小: {final_progress['downloaded_size'] / (1024 * 1024):.1f} MB")
    
    if final_progress['failed'] > 0:
        print(f"错误日志已保存到: {manager.error_file}")
    
    logger.info("批量下载任务完成")

# --- 6. 运行示例 ---
if __name__ == "__main__":
    # 忽略 SSL 警告
    requests.packages.urllib3.disable_warnings(requests.packages.urllib3.exceptions.InsecureRequestWarning)
    
    # 使用示例
    print("=== 高级批量下载系统 ===")
    
    # 示例1: 推荐的稳定配置 - 下载所有图片和音乐
    batch_download_advanced(
        db_path='books.db',  # 替换为你的数据库路径
        table_name='Image',
        condition=None,  # 下载所有
        output_dir='downloads',
        max_workers=2,  # 减少并发，提高稳定性
        max_retries=3,  # 增加重试次数
        download_images=True,
        download_music=True,
        request_delay=2.0,  # 更长延迟
        # connection_timeout=10  # 增加连接超时
    )
    
    # 示例2: 保守配置 - 只下载图片，适合网络不稳定的情况
    # batch_download_advanced(
    #     db_path='your_database.db',
    #     table_name='Image',
    #     condition="id BETWEEN 1 AND 5000",  # 分批下载
    #     output_dir='downloads/images',
    #     max_workers=1,  # 单线程，最稳定
    #     max_retries=8,  # 更多重试
    #     download_images=True,
    #     download_music=False,
    #     request_delay=2.0,  # 更长延迟
    #     connection_timeout=30
    # )
    
    # 示例3: 激进配置 - 快速下载，适合网络稳定的情况
    # batch_download_advanced(
    #     db_path='your_database.db',
    #     table_name='Image',
    #     condition="gid IS NOT NULL",  # 只下载有音乐的项目
    #     output_dir='downloads/music',
    #     max_workers=4,  # 更多并发
    #     max_retries=3,
    #     download_images=False,
    #     download_music=True,
    #     request_delay=0.3,  # 较短延迟
    #     connection_timeout=15
    # )