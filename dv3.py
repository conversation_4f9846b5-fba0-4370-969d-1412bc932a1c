import time
import hashlib
import requests
import re
import urllib.parse
import sqlite3
import os
import json
import threading
import queue
import signal
import sys
import random
from datetime import datetime, timezone, timedelta
from concurrent.futures import ThreadPoolExecutor, as_completed
from typing import List, Dict, Optional, Tuple
import logging

# --- 1. 定义常量和基础配置 ---
APP_ID = "hymn"
APP_KEY = "shigeben"
USER_AGENT = "okhttp/4.9.0"
OS = "android"
PRC = timezone(timedelta(hours=8))

# 多服务器配置
DOWNLOAD_HOSTS = [
    {"group": 3, "type": 3, "url": "https://***************:9021/api"},
    {"group": 1, "type": 3, "url": "https://11.hxiopkse.icu:9013/api"},
    {"group": 2, "type": 1, "url": "https://21.hxiopkse.icu:9013/api"},
    {"group": 3, "type": 3, "url": "https://31.hxiopkse.icu:9013/api"},
    {"group": 3, "type": 3, "url": "https://32.hxiopkse.icu:9013/api"},
    {"group": 3, "type": 1, "url": "https://33.hxiopkse.icu:9013/api"},
    {"group": 3, "type": 1, "url": "https://34.hxiopkse.icu:9013/api"},
    {"group": 3, "type": 3, "url": "https://35.hxiopkse.icu:9013/api"},
]

# 全局停止标志
stop_flag = threading.Event()

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('download.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# --- 2. 信号处理 ---
def signal_handler(signum, frame):
    """处理Ctrl+C信号，优雅退出"""
    logger.info("接收到退出信号，正在优雅关闭...")
    stop_flag.set()
    print("\n正在停止下载，请稍候...")

signal.signal(signal.SIGINT, signal_handler)
signal.signal(signal.SIGTERM, signal_handler)

# --- 3. 算法实现函数 ---
def get_header_timestamp_and_token():
    """生成用于请求头的 timestamp 和 token"""
    ts_header = int(time.time())
    source_string = f"{APP_ID}{APP_KEY}{ts_header}"
    token = hashlib.md5(source_string.encode('utf-8')).hexdigest()
    return ts_header, token.upper()

def get_url_timestamp_and_code(item_id: int):
    """生成用于 URL 的 timestamp 和 code"""
    base_date = datetime(2010, 1, 1, tzinfo=PRC)
    base_ts_millis = int(base_date.timestamp())
    current_ts_millis = int(time.time())
    ts_url = (current_ts_millis - base_ts_millis)
    
    source_string_for_code = f"{ts_url}{item_id}"
    md5_hash = hashlib.md5(source_string_for_code.encode('utf-8')).hexdigest()
    code = md5_hash[12:16]
    
    return ts_url, code.upper()

# --- 4. 服务器调度管理 ---
class ServerScheduler:
    def __init__(self, hosts: List[Dict]):
        self.hosts = hosts
        self.server_stats = {}
        self.lock = threading.Lock()
        self.last_health_check = 0
        self.health_check_interval = 300  # 5分钟检查一次
        
        # 初始化服务器统计
        for host in hosts:
            host_key = self._get_host_key(host['url'])
            self.server_stats[host_key] = {
                'response_times': [],
                'success_count': 0,
                'failure_count': 0,
                'last_used': 0,
                'available': True,
                'avg_response_time': float('inf')
            }
    
    def _get_host_key(self, url: str) -> str:
        """从URL提取主机标识"""
        return url.replace('https://', '').replace('http://', '').split('/')[0]
    
    def _health_check(self, host_url: str, timeout: int = 5) -> Tuple[bool, float]:
        """检查服务器健康状态"""
        try:
            start_time = time.time()
            response = requests.get(f"{host_url}/health", timeout=timeout, verify=False)
            response_time = time.time() - start_time
            return response.status_code == 200, response_time
        except:
            return False, float('inf')
    
    def update_server_stats(self, host_url: str, success: bool, response_time: float):
        """更新服务器统计信息"""
        with self.lock:
            host_key = self._get_host_key(host_url)
            if host_key not in self.server_stats:
                return
            
            stats = self.server_stats[host_key]
            stats['last_used'] = time.time()
            
            if success:
                stats['success_count'] += 1
                stats['response_times'].append(response_time)
                # 只保留最近50次的响应时间
                if len(stats['response_times']) > 50:
                    stats['response_times'] = stats['response_times'][-50:]
                stats['avg_response_time'] = sum(stats['response_times']) / len(stats['response_times'])
            else:
                stats['failure_count'] += 1
                # 连续失败3次标记为不可用
                if stats['failure_count'] >= 3:
                    stats['available'] = False
    
    def get_best_server(self) -> Optional[str]:
        """获取最佳服务器"""
        current_time = time.time()
        
        # 定期进行健康检查
        if current_time - self.last_health_check > self.health_check_interval:
            self._perform_health_checks()
            self.last_health_check = current_time
        
        with self.lock:
            available_servers = []
            
            for host in self.hosts:
                host_key = self._get_host_key(host['url'])
                stats = self.server_stats[host_key]
                
                if not stats['available']:
                    continue
                
                # 计算服务器权重（响应时间越短权重越高）
                weight = 1.0 / (stats['avg_response_time'] + 0.1)
                
                # 考虑最近使用时间，避免过度使用同一服务器
                time_since_last_use = current_time - stats['last_used']
                if time_since_last_use < 1:  # 1秒内使用过的服务器降低权重
                    weight *= 0.5
                
                available_servers.append((host['url'], weight))
            
            if not available_servers:
                # 如果没有可用服务器，重置所有服务器状态
                for stats in self.server_stats.values():
                    stats['available'] = True
                    stats['failure_count'] = 0
                return self.hosts[0]['url'] if self.hosts else None
            
            # 加权随机选择
            total_weight = sum(weight for _, weight in available_servers)
            if total_weight == 0:
                return available_servers[0][0]
            
            rand_val = random.uniform(0, total_weight)
            current_weight = 0
            
            for url, weight in available_servers:
                current_weight += weight
                if rand_val <= current_weight:
                    return url
            
            return available_servers[0][0]
    
    def _perform_health_checks(self):
        """执行健康检查"""
        def check_server(host_url):
            is_healthy, response_time = self._health_check(host_url)
            host_key = self._get_host_key(host_url)
            with self.lock:
                if host_key in self.server_stats:
                    self.server_stats[host_key]['available'] = is_healthy
                    if is_healthy:
                        self.server_stats[host_key]['failure_count'] = 0
        
        # 并行检查所有服务器
        with ThreadPoolExecutor(max_workers=len(self.hosts)) as executor:
            futures = [executor.submit(check_server, host['url']) for host in self.hosts]
            for future in as_completed(futures):
                try:
                    future.result(timeout=10)
                except:
                    pass

# 全局服务器调度器
server_scheduler = ServerScheduler(DOWNLOAD_HOSTS)

# --- 5. 增强的下载管理器 ---
class EnhancedDownloadManager:
    def __init__(self, db_path: str, state_file: str = 'download_state.json'):
        self.db_path = db_path
        self.state_file = state_file
        self.error_file = 'download_errors.json'
        self.partial_file = 'partial_downloads.json'
        self.lock = threading.Lock()
        self.stats = {
            'total_items': 0,
            'completed': 0,
            'failed': 0,
            'skipped': 0,
            'total_size': 0,
            'downloaded_size': 0,
            'start_time': None,
            'errors': [],
            'partial_downloads': {}
        }
        self.load_state()
        self.load_partial_downloads()

    def load_state(self):
        """加载下载状态"""
        if os.path.exists(self.state_file):
            try:
                with open(self.state_file, 'r', encoding='utf-8') as f:
                    saved_state = json.load(f)
                    self.stats.update(saved_state)
                logger.info(f"加载状态: 已完成 {self.stats['completed']}, 失败 {self.stats['failed']}")
            except Exception as e:
                logger.error(f"加载状态文件失败: {e}")

    def load_partial_downloads(self):
        """加载部分下载信息"""
        if os.path.exists(self.partial_file):
            try:
                with open(self.partial_file, 'r', encoding='utf-8') as f:
                    self.stats['partial_downloads'] = json.load(f)
                logger.info(f"加载部分下载信息: {len(self.stats['partial_downloads'])} 个文件")
            except Exception as e:
                logger.error(f"加载部分下载文件失败: {e}")

    def save_state(self):
        """保存下载状态"""
        try:
            with open(self.state_file, 'w', encoding='utf-8') as f:
                json.dump(self.stats, f, ensure_ascii=False, indent=2)
        except Exception as e:
            logger.error(f"保存状态文件失败: {e}")

    def save_partial_downloads(self):
        """保存部分下载信息"""
        try:
            with open(self.partial_file, 'w', encoding='utf-8') as f:
                json.dump(self.stats['partial_downloads'], f, ensure_ascii=False, indent=2)
        except Exception as e:
            logger.error(f"保存部分下载文件失败: {e}")

    def save_error(self, error_info: dict):
        """保存错误信息"""
        try:
            errors = []
            if os.path.exists(self.error_file):
                with open(self.error_file, 'r', encoding='utf-8') as f:
                    errors = json.load(f)

            errors.append({
                **error_info,
                'timestamp': datetime.now().isoformat()
            })

            with open(self.error_file, 'w', encoding='utf-8') as f:
                json.dump(errors, f, ensure_ascii=False, indent=2)
        except Exception as e:
            logger.error(f"保存错误信息失败: {e}")

    def get_download_list(self, table_name: str = 'Image', condition: str = None) -> List[Dict]:
        """获取下载列表"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # 构建查询语句，获取id, gid, fs字段
            base_query = f"SELECT id, gid, COALESCE(fs, 0) as fs FROM {table_name}"
            if condition:
                query = f"{base_query} WHERE {condition}"
            else:
                query = base_query

            cursor.execute(query)
            rows = cursor.fetchall()

            # 构建下载列表
            download_list = []
            for row in rows:
                item_id, gid, fs = row
                # 添加图片下载任务
                download_list.append({
                    'id': item_id,
                    'type': 'image',
                    'size': fs or 0,
                    'gid': gid
                })
                # 如果有gid，添加音乐下载任务
                if gid:
                    download_list.append({
                        'id': gid,
                        'type': 'music',
                        'size': fs or 0,
                        'gid': gid
                    })

            conn.close()
            return download_list

        except Exception as e:
            logger.error(f"获取下载列表失败: {e}")
            return []

    def get_total_size(self, table_name: str = 'Image', condition: str = None) -> int:
        """获取总文件大小"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            base_query = f"SELECT SUM(COALESCE(fs, 0)) FROM {table_name}"
            if condition:
                query = f"{base_query} WHERE {condition}"
            else:
                query = base_query

            cursor.execute(query)
            result = cursor.fetchone()
            conn.close()

            return result[0] if result and result[0] else 0

        except Exception as e:
            logger.error(f"获取总大小失败: {e}")
            return 0

    def is_completed(self, item_id: int, download_type: str, output_dir: str) -> Tuple[bool, int]:
        """检查文件是否已下载完成，返回(是否完成, 已下载大小)"""
        file_extension = "jpg" if download_type == "image" else "mp3"
        file_patterns = [
            f"{download_type}_{item_id}.{file_extension}",
            f"{item_id}.{file_extension}"
        ]

        for pattern in file_patterns:
            file_path = os.path.join(output_dir, pattern)
            if os.path.exists(file_path):
                file_size = os.path.getsize(file_path)
                if file_size > 0:
                    return True, file_size

        # 检查是否有部分下载的文件
        file_key = f"{download_type}_{item_id}"
        if file_key in self.stats['partial_downloads']:
            partial_info = self.stats['partial_downloads'][file_key]
            temp_file = partial_info.get('temp_file')
            if temp_file and os.path.exists(temp_file):
                return False, os.path.getsize(temp_file)

        return False, 0

    def update_progress(self, success: bool, file_size: int = 0, error_info: dict = None):
        """更新下载进度"""
        with self.lock:
            if success:
                self.stats['completed'] += 1
                self.stats['downloaded_size'] += file_size
            else:
                self.stats['failed'] += 1
                if error_info:
                    self.save_error(error_info)

            self.save_state()

    def update_partial_download(self, item_id: int, download_type: str,
                               temp_file: str, downloaded_size: int, total_size: int = 0):
        """更新部分下载信息"""
        with self.lock:
            file_key = f"{download_type}_{item_id}"
            self.stats['partial_downloads'][file_key] = {
                'temp_file': temp_file,
                'downloaded_size': downloaded_size,
                'total_size': total_size,
                'last_update': time.time()
            }
            self.save_partial_downloads()

    def remove_partial_download(self, item_id: int, download_type: str):
        """移除部分下载记录"""
        with self.lock:
            file_key = f"{download_type}_{item_id}"
            if file_key in self.stats['partial_downloads']:
                partial_info = self.stats['partial_downloads'][file_key]
                temp_file = partial_info.get('temp_file')
                if temp_file and os.path.exists(temp_file):
                    try:
                        os.remove(temp_file)
                    except:
                        pass
                del self.stats['partial_downloads'][file_key]
                self.save_partial_downloads()

    def get_progress_info(self) -> Dict:
        """获取进度信息"""
        with self.lock:
            total = self.stats['total_items']
            completed = self.stats['completed']
            failed = self.stats['failed']
            remaining = total - completed - failed

            # 计算预计剩余时间
            if self.stats['start_time'] and completed > 0:
                elapsed = time.time() - self.stats['start_time']
                rate = completed / elapsed
                eta = remaining / rate if rate > 0 else 0
            else:
                eta = 0

            # 计算下载速度
            if self.stats['start_time']:
                elapsed = time.time() - self.stats['start_time']
                speed = self.stats['downloaded_size'] / elapsed if elapsed > 0 else 0
            else:
                speed = 0

            return {
                'total': total,
                'completed': completed,
                'failed': failed,
                'remaining': remaining,
                'percentage': (completed / total * 100) if total > 0 else 0,
                'eta_seconds': eta,
                'speed_bps': speed,
                'downloaded_size': self.stats['downloaded_size'],
                'total_size': self.stats['total_size']
            }

# --- 6. 增强的下载功能 ---
def download_single_file_enhanced(item_id: int, download_type: str, output_dir: str,
                                 manager: EnhancedDownloadManager,
                                 max_retries: int = 3, timeout: int = 30,
                                 request_delay: float = 1.0) -> Tuple[bool, int, Optional[str]]:
    """增强的单文件下载功能，支持多服务器和断点续传"""

    if stop_flag.is_set():
        return False, 0, "用户取消下载"

    # 检查是否已完成或部分下载
    is_complete, existing_size = manager.is_completed(item_id, download_type, output_dir)
    if is_complete:
        return True, existing_size, None

    session = None
    file_extension = "jpg" if download_type == "image" else "mp3"
    file_name = f"{download_type}_{item_id}.{file_extension}"

    # 确保输出目录存在
    os.makedirs(output_dir, exist_ok=True)
    file_path = os.path.join(output_dir, file_name)
    temp_file_path = file_path + ".tmp"

    # 检查是否有部分下载
    resume_from = 0
    if existing_size > 0 and os.path.exists(temp_file_path):
        resume_from = existing_size
        logger.info(f"断点续传: {file_name} 从 {resume_from} 字节开始")

    for attempt in range(max_retries):
        if stop_flag.is_set():
            return False, 0, "用户取消下载"

        # 获取最佳服务器
        server_url = server_scheduler.get_best_server()
        if not server_url:
            return False, 0, "没有可用的服务器"

        start_time = time.time()

        try:
            # 请求延迟
            if request_delay > 0 and attempt > 0:
                time.sleep(request_delay)

            # 创建新的session
            if session:
                session.close()

            session = requests.Session()
            session.verify = False
            session.trust_env = False

            # 配置适配器
            from requests.adapters import HTTPAdapter
            from urllib3.util.retry import Retry

            retry_strategy = Retry(total=0, connect=0, read=0, redirect=0, status_forcelist=[])
            adapter = HTTPAdapter(max_retries=retry_strategy, pool_connections=1, pool_maxsize=1)
            session.mount("http://", adapter)
            session.mount("https://", adapter)

            # 生成请求参数
            ts_header, token = get_header_timestamp_and_token()
            ts_url, code = get_url_timestamp_and_code(item_id)

            # 从服务器URL提取主机
            host = server_url.replace('https://', '').replace('http://', '').split('/')[0]
            final_url = f"{server_url}/download/{download_type}?id={item_id}&timestamp={ts_url}&code={code}"

            headers = {
                "host": host,
                "appid": APP_ID,
                "timestamp": str(ts_header),
                "token": token,
                "os": OS,
                "accept-encoding": "gzip",
                "user-agent": USER_AGENT,
                "Connection": "close",
            }

            # 添加断点续传头
            if resume_from > 0:
                headers["Range"] = f"bytes={resume_from}-"

            # 发起请求
            response = session.get(
                final_url,
                headers=headers,
                stream=True,
                timeout=(10, timeout),
                allow_redirects=True
            )

            response_time = time.time() - start_time

            if response.status_code in [200, 206]:  # 200: 完整文件, 206: 部分内容
                # 更新服务器统计
                server_scheduler.update_server_stats(server_url, True, response_time)

                # 获取文件名
                content_disposition = response.headers.get('content-disposition')
                if content_disposition:
                    filename_star_match = re.search(r"filename\*\=UTF-8''(.+)", content_disposition)
                    if filename_star_match:
                        file_name = urllib.parse.unquote(filename_star_match.group(1))
                    else:
                        fname = re.findall(r'filename="?([^"]+)"?', content_disposition)
                        if fname:
                            file_name = fname[0]

                file_path = os.path.join(output_dir, file_name)
                temp_file_path = file_path + ".tmp"

                # 下载文件
                file_size = resume_from
                write_mode = "ab" if resume_from > 0 else "wb"

                try:
                    with open(temp_file_path, write_mode) as f:
                        for chunk in response.iter_content(chunk_size=8192):
                            if stop_flag.is_set():
                                # 保存部分下载信息
                                manager.update_partial_download(item_id, download_type,
                                                              temp_file_path, file_size)
                                return False, 0, "用户取消下载"

                            if chunk:
                                f.write(chunk)
                                file_size += len(chunk)

                    # 下载完成，重命名文件
                    if os.path.exists(file_path):
                        os.remove(file_path)
                    os.rename(temp_file_path, file_path)

                    # 移除部分下载记录
                    manager.remove_partial_download(item_id, download_type)

                    logger.info(f"下载成功: {file_name} ({file_size} bytes) 服务器: {host}")
                    return True, file_size, None

                except Exception as write_error:
                    # 保存部分下载信息
                    if os.path.exists(temp_file_path):
                        current_size = os.path.getsize(temp_file_path)
                        manager.update_partial_download(item_id, download_type,
                                                      temp_file_path, current_size)
                    raise write_error

            else:
                # 更新服务器统计
                server_scheduler.update_server_stats(server_url, False, response_time)
                error_msg = f"HTTP {response.status_code}: {response.text[:200]}"
                if attempt == max_retries - 1:
                    logger.error(f"下载失败 (ID: {item_id}, Type: {download_type}): {error_msg}")
                    return False, 0, error_msg
                else:
                    logger.warning(f"下载失败，重试 {attempt + 1}/{max_retries} (ID: {item_id}): {error_msg}")
                    delay = (2 ** attempt) + random.uniform(0, 1)
                    time.sleep(delay)

        except Exception as e:
            # 更新服务器统计
            server_scheduler.update_server_stats(server_url, False, float('inf'))
            error_msg = f"网络错误: {str(e)}"
            if attempt == max_retries - 1:
                logger.error(f"下载异常 (ID: {item_id}, Type: {download_type}): {error_msg}")
                return False, 0, error_msg
            else:
                logger.warning(f"下载异常，重试 {attempt + 1}/{max_retries} (ID: {item_id}): {error_msg}")
                delay = (3 ** attempt) + random.uniform(1, 3)
                time.sleep(delay)

        finally:
            if session:
                session.close()

    return False, 0, "重试次数已用完"

def download_worker_enhanced(download_queue: queue.Queue, manager: EnhancedDownloadManager,
                           output_dir: str, max_retries: int, request_delay: float,
                           connection_timeout: int):
    """增强的下载工作线程"""
    while not stop_flag.is_set():
        try:
            item = download_queue.get_nowait()
        except queue.Empty:
            break

        item_id = item['id']
        download_type = item['type']
        expected_size = item['size']

        # 检查是否已完成
        is_complete, existing_size = manager.is_completed(item_id, download_type, output_dir)
        if is_complete:
            manager.update_progress(True, existing_size)
            manager.stats['skipped'] += 1
            logger.info(f"跳过已存在文件: {download_type}_{item_id}")
            download_queue.task_done()
            continue

        # 下载文件
        success, file_size, error_msg = download_single_file_enhanced(
            item_id, download_type, output_dir, manager, max_retries,
            connection_timeout, request_delay
        )

        # 更新进度
        error_info = None
        if not success:
            error_info = {
                'id': item_id,
                'type': download_type,
                'error': error_msg,
                'expected_size': expected_size
            }

        manager.update_progress(success, file_size, error_info)
        download_queue.task_done()

def progress_monitor_enhanced(manager: EnhancedDownloadManager, update_interval: int = 5):
    """增强的进度监控线程"""
    while not stop_flag.is_set():
        try:
            progress = manager.get_progress_info()

            if progress['remaining'] <= 0:
                break

            # 格式化输出
            percentage = progress['percentage']
            completed = progress['completed']
            total = progress['total']
            failed = progress['failed']
            remaining = progress['remaining']

            # 格式化时间
            eta_seconds = progress['eta_seconds']
            if eta_seconds > 0:
                eta_hours = int(eta_seconds // 3600)
                eta_minutes = int((eta_seconds % 3600) // 60)
                eta_str = f"{eta_hours:02d}:{eta_minutes:02d}:{int(eta_seconds % 60):02d}"
            else:
                eta_str = "计算中..."

            # 格式化速度
            speed = progress['speed_bps']
            if speed > 1024 * 1024:
                speed_str = f"{speed / (1024 * 1024):.1f} MB/s"
            elif speed > 1024:
                speed_str = f"{speed / 1024:.1f} KB/s"
            else:
                speed_str = f"{speed:.1f} B/s"

            # 格式化大小
            downloaded_mb = progress['downloaded_size'] / (1024 * 1024)
            total_mb = progress['total_size'] / (1024 * 1024)

            print(f"\r进度: {percentage:.1f}% ({completed}/{total}) "
                  f"失败: {failed} 剩余: {remaining} "
                  f"ETA: {eta_str} 速度: {speed_str} "
                  f"大小: {downloaded_mb:.1f}/{total_mb:.1f} MB", end='', flush=True)

            time.sleep(update_interval)

        except Exception as e:
            logger.error(f"进度监控错误: {e}")
            break

# --- 7. 主要下载函数 ---
def batch_download_ultimate(db_path: str, table_name: str = 'Image',
                           condition: str = None, output_dir: str = 'downloads',
                           max_workers: int = 5, max_retries: int = 3,
                           download_images: bool = True, download_music: bool = True,
                           request_delay: float = 1.0, connection_timeout: int = 30):
    """终极批量下载函数，支持多服务器调度、断点续传、优雅退出"""

    # 初始化管理器
    manager = EnhancedDownloadManager(db_path)

    # 获取下载列表
    logger.info("获取下载列表...")
    all_items = manager.get_download_list(table_name, condition)

    # 过滤下载类型
    download_items = []
    for item in all_items:
        if (item['type'] == 'image' and download_images) or \
           (item['type'] == 'music' and download_music):
            download_items.append(item)

    if not download_items:
        logger.info("没有找到需要下载的项目")
        return

    # 初始化统计信息
    manager.stats['total_items'] = len(download_items)
    manager.stats['total_size'] = manager.get_total_size(table_name, condition)
    manager.stats['start_time'] = time.time()

    logger.info(f"开始下载 {len(download_items)} 个项目")
    logger.info(f"预计总大小: {manager.stats['total_size'] / (1024 * 1024):.1f} MB")
    logger.info(f"使用 {len(DOWNLOAD_HOSTS)} 个下载服务器")

    # 创建下载队列
    download_queue = queue.Queue()
    for item in download_items:
        download_queue.put(item)

    # 启动进度监控
    progress_thread = threading.Thread(target=progress_monitor_enhanced, args=(manager, 5))
    progress_thread.daemon = True
    progress_thread.start()

    # 启动下载线程
    try:
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            futures = []
            for _ in range(max_workers):
                future = executor.submit(download_worker_enhanced, download_queue, manager,
                                       output_dir, max_retries, request_delay, connection_timeout)
                futures.append(future)

            # 等待所有任务完成或用户中断
            for future in as_completed(futures):
                try:
                    future.result()
                except Exception as e:
                    logger.error(f"下载线程异常: {e}")

                if stop_flag.is_set():
                    break

    except KeyboardInterrupt:
        logger.info("接收到中断信号")
        stop_flag.set()

    # 等待进度监控结束
    progress_thread.join(timeout=1)

    # 最终统计
    final_progress = manager.get_progress_info()
    print(f"\n\n=== 下载完成 ===")
    print(f"总计: {final_progress['total']}")
    print(f"成功: {final_progress['completed']}")
    print(f"失败: {final_progress['failed']}")
    print(f"跳过: {manager.stats['skipped']}")

    if final_progress['total'] > 0:
        success_rate = final_progress['completed'] / final_progress['total'] * 100
        print(f"成功率: {success_rate:.1f}%")

    print(f"下载大小: {final_progress['downloaded_size'] / (1024 * 1024):.1f} MB")

    # 显示部分下载信息
    partial_count = len(manager.stats['partial_downloads'])
    if partial_count > 0:
        print(f"部分下载: {partial_count} 个文件（重启程序可继续下载）")

    if final_progress['failed'] > 0:
        print(f"错误日志已保存到: {manager.error_file}")

    logger.info("批量下载任务完成")

# --- 8. 运行示例 ---
if __name__ == "__main__":
    # 忽略 SSL 警告
    requests.packages.urllib3.disable_warnings(requests.packages.urllib3.exceptions.InsecureRequestWarning)

    print("=== 终极批量下载系统 v3.0 ===")
    print("特性: 多服务器调度 | 断点续传 | 优雅退出 | 智能重试")
    print("按 Ctrl+C 可优雅退出并保存进度")
    print()

    try:
        # 推荐配置 - 平衡性能和稳定性
        batch_download_ultimate(
            db_path='books.db',
            table_name='Image',
            condition=None,  # 下载所有
            output_dir='downloads',
            max_workers=3,  # 适中的并发数
            max_retries=5,  # 增加重试次数
            download_images=True,
            download_music=True,
            request_delay=0.5,  # 请求间隔
            connection_timeout=30  # 连接超时
        )
    except Exception as e:
        logger.error(f"程序异常: {e}")
        print(f"程序异常: {e}")
    finally:
        print("\n程序已退出")
